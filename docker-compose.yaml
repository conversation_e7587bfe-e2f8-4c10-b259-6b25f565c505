services:
  etcd:
    image: bitnami/etcd:3.5.5
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes
      - ETCD_ADVERTISE_CLIENT_URLS=http://0.0.0.0:2379
    ports:
      - "2379:2379"
      - "2380:2380"
  etcdkeeper:
    image: evildecay/etcdkeeper:v0.7.6
    ports:
      - "8099:8080"
    depends_on:
    - etcd
  comet:
      image: dev-dockerhub.papegames.com/platform/comet/comet:0.0.1
      mem_limit: 16G
      mem_reservation: 16G
      cpus: 8
      ports:
      - "5000:5000"
      - "5001:5001"
      - "5002:5002"
      - "48474:48474"
      volumes:
      - /data/app/comet/conf.yaml:/app/conf.yaml
      - /data/app/comet/logs:/app/logs
      depends_on:
      - etcd
  comet-graph:
      image: dev-dockerhub.papegames.com/platform/comet/comet-graph:0.0.1
      ports:
      - "9696:9696"
      - "48473:48473"
      volumes:
      - /data/app/comet-graph/conf.yaml:/app/conf.yaml
      - /data/app/comet-graph/logs:/app/logs
      depends_on:
      - etcd