### 事务性消息实现方案

该方案旨在为长连接网关添加事务性消息功能，确保消息的“至少一次”送达。

#### 1. 核心目标

*   **可靠性**: 保证带有 `transaction_id` 的消息至少被客户端成功接收一次。
*   **协议扩展**: 增加客户端到服务端的 `Acknowledge` (ACK) 协议，用于消息送达确认。
*   **状态管理**: 服务端需要记录待确认的事务性消息状态。
*   **重试机制**: 对于未在规定时间内收到 ACK 的消息，服务端需要进行重试。

#### 2. 协议设计

我们已经在 `comet.proto` 中定义了 `Acknowledge` 消息，这里再次确认其结构。

**客户端 -> 服务端 (Acknowledge)**

```json
{
  "command": "acknowledge",
  "version": "1",
  "request_id": "some-random-id",
  "option": {
    "transaction_id": "the-id-of-the-message-to-ack"
  }
}
```

此消息由客户端在成功接收并处理完一个事务性消息后，发送给服务端。

#### 3. 服务端实现方案

##### 3.1. 数据流与核心流程

###### 正常流程 (消息成功送达并确认)

```mermaid
sequenceDiagram
    participant Biz as 业务服务
    participant Comet as 网关
    participant Redis
    participant Client as 客户端

    Biz->>Comet: 1. 发送事务性消息 (含 transaction_id)
    Comet->>Redis: 2. 存储待确认消息 (Key: pending_ack:user_id:tx_id)
    Comet->>Client: 3. 下发消息
    Client->>Client: 4. 处理消息
    Client->>Comet: 5. 发送 ACK 确认消息
    Comet->>Redis: 6. 删除待确认消息
```

1.  **发送事务性消息**:
    *   当业务方需要通过网关下发一条消息时，如果消息中包含了 `transaction_id`，则网关视其为事务性消息。
    *   在将消息通过 WebSocket (或其他连接) 发送给客户端**之前**，服务端必须将该消息持久化。推荐使用 Redis。
    *   **Redis 存储**:
        *   **Key**: `pending_ack:<user_id>:<transaction_id>`
        *   **Value**: 完整的消息内容 (JSON 字符串)。
        *   **Type**: `String` 或 `Hash`。
        *   **Expiration**: 设置一个合理的过期时间 (例如：5分钟)，作为消息重试的触发器。这可以利用 Redis 的 Keyspace Notifications (键空间通知) 来实现，或者通过一个后台任务来轮询。
    *   持久化成功后，消息通过长连接被发送给客户端。

2.  **处理 ACK 消息**:
    *   在 `control/ws_control.go` (或类似的消息处理入口) 中，增加一个 `case` 来处理 `command: "acknowledge"` 的消息。
    *   收到 ACK 消息后，解析出 `transaction_id` 和对应的 `user_id`。
    *   从 Redis 中删除对应的待确认消息记录：`DEL pending_ack:<user_id>:<transaction_id>`。
    *   删除成功，表示该事务性消息的生命周期结束。

##### 3.2. 重试与离线消息处理

###### 异常流程 (ACK 未收到，触发重试)

```mermaid
sequenceDiagram
    participant Poller as 后台轮询任务
    participant Comet as 网关
    participant Redis
    participant Client as 客户端

    Poller->>Redis: 1. 定期扫描 pending_ack:*
    Redis-->>Poller: 2. 返回未删除的 Key
    Poller->>Comet: 3. 触发重试逻辑
    Comet->>Client: 4. 重新下发消息
    Client->>Comet: 5. 发送 ACK
    Comet->>Redis: 6. 删除待确认消息
```

1.  **重试机制**:
    *   **方案A (推荐 - 轮询)**: 启动一个后台 Goroutine，该任务会定期 (例如，每分钟) 扫描 Redis 中所有 `pending_ack:*` 的 key。如果某个 key 存在（意味着未被 ACK 删除），则重新向该 `user_id` 的客户端下发消息。为了避免无限重试，可以为每个消息增加一个重试计数字段。
    *   **方案B (Keyspace Notifications)**: 配置 Redis，当 `pending_ack:*` 的 key 过期时发送一个事件。订阅这个事件的服务可以获取到 `transaction_id`，然后进行重试。此方案更实时，但配置相对复杂。

###### 离线重连流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Comet as 网关
    participant Redis

    Client->>Comet: 1. 建立新连接
    Comet->>Redis: 2. 检查该用户的 pending_ack
    Redis-->>Comet: 3. 返回离线期间未 ACK 的消息
    Comet->>Client: 4. 立即重新下发消息
```

2.  **用户重连**:
    *   当一个用户重新建立连接时 (在 `server/ws_server.go` 的连接建立逻辑中)，服务端应立即检查 Redis 中是否存在该用户的待确认消息 (`SCAN 0 MATCH pending_ack:<user_id>:*`)。
    *   如果存在，则立即将这些消息重新发送给客户端。

##### 3.3. 代码修改点

1.  **`proto/comet.proto`**: (已完成) 添加 `Acknowledge` 消息定义。
2.  **`proto/comet.pb.go`, `proto/comet_grpc.pb.go`**: (已完成) 重新生成 gRPC 代码。
3.  **`control/ws_control.go`**: 
    *   在消息处理的 `switch` 语句中增加 `case "acknowledge":`。
    *   调用一个新的 `service` 方法，例如 `service.AcknowledgeMessage(userID, transactionID)`。
4.  **`service/service.go`**:
    *   增加 `AcknowledgeMessage` 方法，该方法负责执行 Redis 的 `DEL` 操作。
    *   修改现有的消息下发方法。在发送消息前，检查 `transaction_id`，如果存在，则先执行 Redis 的 `SET` 操作。
5.  **`main.go` 或 `service/service.go`**:
    *   在程序启动时，初始化并启动用于轮询重试的后台 Goroutine。
6.  **`server/ws_server.go`**:
    *   在处理新连接的逻辑中，增加检查并重发离线事务性消息的步骤。
7.  **`config/conf.go`**:
    *   可能需要增加 Redis 的相关配置项。

#### 4. 客户端职责

*   客户端在收到带有 `transaction_id` 的消息后，必须向服务端发送 `Acknowledge` 消息。
*   由于是“至少一次”送达，客户端需要能够处理重复的消息。可以通过本地记录已处理的 `transaction_id` 来实现幂等性。