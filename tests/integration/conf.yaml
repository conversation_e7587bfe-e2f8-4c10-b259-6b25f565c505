register: false
host: 
  ws: "127.0.0.1:5000"
  grpc: "127.0.0.1:5001"
  api: "127.0.0.1:5002"
report_status: true    
biz:
  sdk: "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6" #jwt secret
comet:
  heartbeat_interval: 90s
  broadcast_buffer_size: 256 #chan broadcast buffer size
  broadcast_parallel: 100 #broadcase gorutine size
  unicast_buffer_size: 256 #chan  unicast buffer size
  unicast_parallel: 100 #unicast gorutine size
  concurrent_message_handling: false
  write_timeout: 1s
comet_graph:
  sd_name: "comet-graph.xgrpc"
sparrow:
  log:
    color: false
    file: "./logs/comet"
    level: "debug"
    encoding: "console"
    buffer: 4096
    rotate: "daily"
  registrar:
    prefix: "/sparrow/comet/"
    addrs:
      - "*************:2379"
  database:    
    redis:
      addr: *************:6379
      password:
      pool_size: 5
      max_retries: 2  