package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	pb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	xcipher "gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
)

func wsConnect(t *testing.T, channel string) (*websocket.Conn, error) {
	u := url.URL{Scheme: "ws", Host: config.Get().Host.WS, Path: "/ws"}
	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		t.Fatal(err)
	}
	jwtToken, err := xcipher.NewToken("HS256", "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6", "sdk")
	if err != nil {
		t.Fatal(err)
	}
	now := time.Now()
	ttl := 24 * time.Hour
	cliams := new(jwt.Claims)
	cliams.Audience = "sparrow"
	cliams.ExpiresAt = now.Add(ttl).Unix()
	cliams.Scopes = []string{channel}
	cliams.Subject = "subject"
	token, err := jwtToken.Sign(cliams)
	if err != nil {
		t.Fatal(err)
	}

	subMsg := core.StreamMessage{
		Header: &core.Header{
			Command: core.CmdTypeSub,
			Option: core.Option{
				Channel: channel,
			},
			Version: core.Ver,
		},
		Payload: []byte("[\"" + token + "\"]"),
	}

	b, err := subMsg.ToBytes()
	if err != nil {
		t.Log("send:", err)
		return nil, err
	}
	c.WriteMessage(websocket.BinaryMessage, b)

	_, message, err := c.ReadMessage()
	if websocket.IsCloseError(err) {
		t.Fatal(err)
	}
	if err != nil {
		t.Log("read:", err)
		return nil, err
	}
	h, m, _ := core.Parse(message)
	if h.Command == core.CmdTypeMsg {
		if len(m) > 0 {
			t.Logf("recv: %s", m)
		}
	}
	if h.Command != core.CmdTypeSub {
		return nil, err
	}
	return c, nil
}

func wsReadMessage(ws *websocket.Conn, t *testing.T) (*core.Header, []byte, error) {
	ws.SetReadDeadline(time.Now().Add(2 * time.Second))
	_, message, err := ws.ReadMessage()
	if websocket.IsCloseError(err) {
		panic(err)
	}
	if err != nil {
		return nil, nil, err
	}
	h, m, _ := core.Parse(message)
	if h.Command == core.CmdTypeMsg {
		if len(m) > 0 {
			t.Logf("recv: %s", m)
		}
	}
	return h, m, nil
}

func wsReadResponse(ws *websocket.Conn, t *testing.T) (*core.Response, error) {
	ws.SetReadDeadline(time.Now().Add(2 * time.Second))
	_, message, err := ws.ReadMessage()
	if websocket.IsCloseError(err) {
		panic(err)
	}
	if err != nil {
		return nil, err
	}
	response := new(core.Response)
	err = json.Unmarshal(message, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func TestBroadcast(t *testing.T) {
	ctx := context.Background()
	ws1, err := wsConnect(t, "sdk#awe:transfer:1033:2300434/rw")
	if err != nil {
		t.Fatal(err)
	}
	defer ws1.Close()
	ws2, err := wsConnect(t, "sdk#awe:transfer:1033:2300435/rw")
	if err != nil {
		t.Fatal(err)
	}
	defer ws2.Close()
	client := getGrpcClient(ctx)
	payload := []byte("hello")
	Convey("test broadcast", t, func(c C) {
		msg := core.StreamMessage{
			Header: &core.Header{
				Command: core.CmdTypeMsg,
				Version: "1",
				Option: core.Option{
					Channel: "sdk#awe:transfer:1033:2300434/rw",
				},
			},
			Payload: payload,
		}
		b, err := msg.ToBytes()
		if err != nil {
			panic(err)
		}
		_, err = client.Broadcast(context.TODO(), &pb.BCRequest{Message: b})
		c.So(err, ShouldBeNil)
		_, m, err := wsReadMessage(ws1, t)
		c.So(err, ShouldBeNil)
		c.So(m, ShouldEqual, payload)
		_, m, err = wsReadMessage(ws2, t)
		c.So(err, ShouldNotBeNil)
		c.So(m, ShouldEqual, []byte(nil))
	})
}

func TestMultiBroadcast(t *testing.T) {
	ctx := context.Background()
	ws1, err := wsConnect(t, "sdk#1001:1/rw")
	if err != nil {
		t.Fatal(err)
	}
	defer ws1.Close()
	client := getGrpcClient(ctx)
	payload := []byte("hello")
	Convey("test broadcast", t, func(c C) {
		msg := core.StreamMessage{
			Header: &core.Header{
				Command: core.CmdTypeMsg,
				Version: "1",
				Option: core.Option{
					Channel: "sdk#1001:1/rw",
				},
			},
			Payload: payload,
		}
		b, err := msg.ToBytes()
		if err != nil {
			panic(err)
		}
		times := 1000
		for i := 0; i < times; i++ {
			_, err = client.Broadcast(context.TODO(), &pb.BCRequest{Message: b})
			c.So(err, ShouldBeNil)
		}
		for i := 0; i < times; i++ {
			_, m, err := wsReadMessage(ws1, t)
			c.So(err, ShouldBeNil)
			c.So(m, ShouldEqual, payload)
		}
	})
}

func TestUpstramData(t *testing.T) {
	channel := "sdk#1001:1/rw"
	ws1, err := wsConnect(t, channel)
	if err != nil {
		t.Fatal(err)
	}
	defer ws1.Close()
	Convey("test upstream data", t, func(c C) {
		pubMsg := core.Header{
			Command: core.CmdTypeMsg,
			Option: core.Option{
				Channel: channel,
			},
			Version: core.Ver,
		}
		b, err := pubMsg.ToBytes()
		So(err, ShouldBeNil)
		payload := append(b, []byte("\r\n")...)
		for i := 0; i < 100; i++ {
			data := append(payload, []byte(fmt.Sprintf("say hello from ws client(%d)", i))...)
			err = ws1.WriteMessage(websocket.BinaryMessage, data)
			So(err, ShouldBeNil)
			response, err := wsReadResponse(ws1, t)
			So(err, ShouldBeNil)
			So(response.Code, ShouldEqual, 0)
		}
	})
}
