package integration

import (
	"context"
	"fmt"
	"log"
	"net"
	"os"
	"sync"
	"testing"
	"time"

	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/database"
	"gitlab.papegames.com/fringe/comet/server"
	"gitlab.papegames.com/fringe/comet/service"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	"gitlab.papegames.com/fringe/sparrow"

	"gitlab.papegames.com/fringe/sparrow/pkg/xnet"

	pb "gitlab.papegames.com/fringe/comet-graph/proto"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func TestMain(m *testing.M) {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		jwt.Startup,
		service.Startup,
		server.Startup,
		database.Startup,
	).Server(server.Get())

	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		app.WithoutRedirectStderr().Launch()
	}()
	time.Sleep(time.Second)
	app.WaitReady(context.Background())
	fmt.Println("TestMain start")
	c := m.Run()
	app.Stop()
	wg.Wait()
	fmt.Println("TestMain done")
	os.Exit(c)
}

func getGrpcClient(ctx context.Context) pb.CometClient {
	host, port, err := net.SplitHostPort(config.Get().Host.Api)
	if err != nil {
		panic(err)
	}
	if host == "internal" {
		ip, err := xnet.ExternalIP()
		if err != nil {
			panic(err)
		}
		host = ip
	}
	addr := fmt.Sprintf("%s:%s", host, port)
	conn, err := grpc.DialContext(ctx, addr,
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Printf("error connecting to server: %v", err)
	}
	client := pb.NewCometClient(conn)
	return client
}
