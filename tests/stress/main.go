package main

import (
	"crypto/rand"
	"flag"
	"fmt"
	"log"
	mathrand "math/rand"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	xcipher "gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
)

var addr = flag.String("addr", "**************:5000", "http service address")
var isTLS = flag.Bool("tls", false, "use websocket over tls")
var count = flag.Int("c", 20000, "count")
var t = flag.String("t", "hb", "type defaut hb(heartbeat) allowed: hb,msg")
var size = flag.Int("b", 1024, "body size")
var intval = flag.Int("i", 1, "interval seconds")
var r = flag.Bool("r", true, "random channel")
var v = flag.Bool("v", false, "verbose")

func runner() {
	Scheme := "ws"
	if *isTLS {
		Scheme = "wss"
	}
	u := url.URL{Scheme: Scheme, Host: *addr, Path: "/ws"}
	log.Printf("connecting to %s", u.String())

	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Println("dial:", err)
		return
	}
	defer c.Close()

	done := make(chan struct{})

	jwtToken, err := xcipher.NewToken("HS256", "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6")
	if err != nil {
		log.Println("dial:", err)
		return
	}
	now := time.Now()
	ttl := 60 * time.Second
	cliams := new(jwt.Claims)
	cliams.Audience = "sparrow"
	cliams.ExpiresAt = now.Add(ttl).Unix()
	channel := genCH(*r)
	cliams.Scopes = []string{channel}
	cliams.Subject = "subject"
	token, err := jwtToken.Sign(cliams)
	if err != nil {
		panic(err)
	}
	go func() {
		defer close(done)
		for {
			_, message, err := c.ReadMessage()
			if websocket.IsCloseError(err) {
				log.Println("read:", err)
				return
			}
			if err != nil {
				log.Println("read:", err)
				return
			}

			h, m, _ := core.Parse(message)
			if h.Command == core.CmdTypeMsg {
				if len(m) > 0 {
					log.Printf("recv: %s", m)
				}
			}
			if *v {
				log.Printf("recv: %s", message)
			}
		}
	}()

	subMsg := core.StreamMessage{
		Header: &core.Header{
			Command: core.CmdTypeSub,
			Option: core.Option{
				Channel: channel,
			},
			Version: core.Ver,
		},
		Payload: []byte("[\"" + token + "\"]"),
	}

	b, err := subMsg.ToBytes()
	if err != nil {
		log.Println("send:", err)
		return
	}

	c.WriteMessage(websocket.BinaryMessage, b)

	if *t == "hb" {
		Hb(c)
	} else if *t == "msg" {
		Msg(c, channel)
	}
}

func main() {
	mathrand.New(mathrand.NewSource(time.Now().UnixNano()))
	flag.Parse()
	wg := sync.WaitGroup{}
	for i := 0; i < *count; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			runner()
		}() // runner()
	}
	wg.Wait()
}

func Hb(c *websocket.Conn) {
	pubMsg := core.Header{
		Command: core.CmdTypeHb,
		Version: core.Ver,
	}
	b, err := pubMsg.ToBytes()
	if err != nil {
		panic(err)
	}
	for {
		time.Sleep(time.Duration(*intval) * time.Second)
		err := c.WriteMessage(websocket.BinaryMessage, b)
		if err != nil {
			log.Println("send:", err)
		}
	}
}

func Msg(c *websocket.Conn, ch string) {
	pubMsg := core.Header{
		Command: core.CmdTypeMsg,
		Version: core.Ver,
		Option: core.Option{
			Channel: ch,
		},
	}
	b, err := pubMsg.ToBytes()
	if err != nil {
		panic(err)
	}

	payload := append(b, []byte("\r\n")...)
	body := make([]byte, *size)
	// then we can call rand.Read.
	_, err = rand.Read(body)
	if err != nil {
		panic(err)
	}
	payload = append(payload, body...)
	for {
		time.Sleep(time.Duration(*intval) * time.Second)
		c.WriteMessage(websocket.BinaryMessage, payload)
	}
}

func genCH(r bool) string {
	if r {
		return fmt.Sprintf("test#1001:%d/rw", mathrand.Intn(1000000))
	}
	return "test#1001:1/rw"
}
