register: true
host: 
  ws: "0.0.0.0:5000"
  grpc: "0.0.0.0:5001"
  api: "internal:5002"
report_status: true  
biz:
  sdk: "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6" #jwt secret
comet:
  heartbeat_interval: 90s
  broadcast_buffer_size: 256 #chan broadcast buffer size
  broadcast_parallel: 100 #broadcase gorutine size
  unicast_buffer_size: 256 #chan  unicast buffer size
  unicast_parallel: 100 #unicast gorutine size
  concurrent_message_handling: false
  write_timeout: 1s
comet_graph:
  sd_name: "comet-graph.xgrpc"
sparrow:
  log:
    color: false
    file: "./logs/comet"
    level: "debug"
    encoding: "console"
    buffer: 4096
    rotate: "daily"
  registrar:
    prefix: "/sparrow/comet/"
    addrs:
      - "*************:2379"
  govern:
    enable: true
    host: "internal:8091"
  trace:
    Enable: true
    Async: true
    Endpoint: "*************:4317"
  database:    
    redis:
      addr: *************:6379
      password:
      pool_size: 5
      max_retries: 2  