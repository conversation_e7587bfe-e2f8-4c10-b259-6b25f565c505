apiVersion: v1
data:
  conf.yaml: |
    sparrow:
      configure:
        provider: "apollo"
        watch: true
        path: "application.yaml"
        config:
          endpoints:
            - "http://*************:8080"
          appID: "comet"
          cluster: "default"
          namespace: "application"
          secret: "d31736ddbf8343ce8c079f5f888eeddc"
kind: ConfigMap
metadata:
  name: comet
  namespace: platsdk
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: comet
  name: comet
  namespace: platsdk
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: http
  selector:
    app: comet
  type: ClusterIP

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: comet
  name: comet
  namespace: platsdk
spec:
  replicas: 1
  selector:
    matchLabels:
      app: comet
  template:
    metadata:
      annotations:
        k8s.aliyun.com/eci-eviction-enable: 'true'
        k8s.aliyun.com/eci-extra-ephemeral-storage: 100G
        prometheus.io/path: /metrics
        prometheus.io/port: '4006'
        prometheus.io/scrape: 'true'
        sidecar.istio.io/inject: 'false'
      labels:
        app: comet
    spec:
      containers:
        - env:
            - name: APP_NAME
              value: comet
            - name: APP_ENV
              value: dev
            - name: ROLE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['role']
            - name: TZ
              value: Asia/Shanghai
          image: dev-registry-vpc.cn-hangzhou.cr.aliyuncs.com/platform/comet:VERSION
          imagePullPolicy: Always
          livenessProbe:
            initialDelaySeconds: 45
            periodSeconds: 12
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          name: comet
          ports:
            - containerPort: 80
              name: http
              protocol: TCP
          readinessProbe:
            initialDelaySeconds: 20
            periodSeconds: 5
            tcpSocket:
              port: 80
            timeoutSeconds: 5
          securityContext: { }
          volumeMounts:
            - mountPath: /data/logs
              name: k8s-log
            - mountPath: /app/conf.yaml
              name: comet
              subPath: conf.yaml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: lingyun
      restartPolicy: Always
      securityContext: { }
      serviceAccountName: default
      terminationGracePeriodSeconds: 45
      volumes:
        - emptyDir: { }
          name: k8s-log
        - configMap:
            name: comet
          name: comet
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: comet
  namespace: netops
spec:
  gateways:
    - common-inbound-gateway
  hosts:
    - comet-dev.papegames.com
  http:
    - route:
        - destination:
            host: comet.platsdk.svc.cluster.local
            port:
              number: 80
          weight: 100