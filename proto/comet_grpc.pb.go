// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.25.1
// source: proto/comet.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CometService_Handle_FullMethodName = "/comet.proto.CometService/Handle"
)

// CometServiceClient is the client API for CometService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CometServiceClient interface {
	Handle(ctx context.Context, opts ...grpc.CallOption) (CometService_HandleClient, error)
}

type cometServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCometServiceClient(cc grpc.ClientConnInterface) CometServiceClient {
	return &cometServiceClient{cc}
}

func (c *cometServiceClient) Handle(ctx context.Context, opts ...grpc.CallOption) (CometService_HandleClient, error) {
	stream, err := c.cc.NewStream(ctx, &CometService_ServiceDesc.Streams[0], CometService_Handle_FullMethodName, opts...)
	if err != nil {
		return nil, err
	}
	x := &cometServiceHandleClient{stream}
	return x, nil
}

type CometService_HandleClient interface {
	Send(*HandleRequest) error
	Recv() (*HandleResponse, error)
	grpc.ClientStream
}

type cometServiceHandleClient struct {
	grpc.ClientStream
}

func (x *cometServiceHandleClient) Send(m *HandleRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *cometServiceHandleClient) Recv() (*HandleResponse, error) {
	m := new(HandleResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// CometServiceServer is the server API for CometService service.
// All implementations must embed UnimplementedCometServiceServer
// for forward compatibility
type CometServiceServer interface {
	Handle(CometService_HandleServer) error
	mustEmbedUnimplementedCometServiceServer()
}

// UnimplementedCometServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCometServiceServer struct {
}

func (UnimplementedCometServiceServer) Handle(CometService_HandleServer) error {
	return status.Errorf(codes.Unimplemented, "method Handle not implemented")
}
func (UnimplementedCometServiceServer) mustEmbedUnimplementedCometServiceServer() {}

// UnsafeCometServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CometServiceServer will
// result in compilation errors.
type UnsafeCometServiceServer interface {
	mustEmbedUnimplementedCometServiceServer()
}

func RegisterCometServiceServer(s grpc.ServiceRegistrar, srv CometServiceServer) {
	s.RegisterService(&CometService_ServiceDesc, srv)
}

func _CometService_Handle_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(CometServiceServer).Handle(&cometServiceHandleServer{stream})
}

type CometService_HandleServer interface {
	Send(*HandleResponse) error
	Recv() (*HandleRequest, error)
	grpc.ServerStream
}

type cometServiceHandleServer struct {
	grpc.ServerStream
}

func (x *cometServiceHandleServer) Send(m *HandleResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *cometServiceHandleServer) Recv() (*HandleRequest, error) {
	m := new(HandleRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// CometService_ServiceDesc is the grpc.ServiceDesc for CometService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CometService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "comet.proto.CometService",
	HandlerType: (*CometServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "Handle",
			Handler:       _CometService_Handle_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "proto/comet.proto",
}
