// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.25.1
// source: proto/comet.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HandleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []byte `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HandleRequest) Reset() {
	*x = HandleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleRequest) ProtoMessage() {}

func (x *HandleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleRequest.ProtoReflect.Descriptor instead.
func (*HandleRequest) Descriptor() ([]byte, []int) {
	return file_proto_comet_proto_rawDescGZIP(), []int{0}
}

func (x *HandleRequest) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

type HandleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message []byte `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *HandleResponse) Reset() {
	*x = HandleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_comet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HandleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleResponse) ProtoMessage() {}

func (x *HandleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_comet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleResponse.ProtoReflect.Descriptor instead.
func (*HandleResponse) Descriptor() ([]byte, []int) {
	return file_proto_comet_proto_rawDescGZIP(), []int{1}
}

func (x *HandleResponse) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

var File_proto_comet_proto protoreflect.FileDescriptor

var file_proto_comet_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x29, 0x0a, 0x0d, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x2a, 0x0a, 0x0e, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x32, 0x57, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x65, 0x74,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x47, 0x0a, 0x06, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x63, 0x6f, 0x6d, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01,
	0x42, 0x2f, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x70, 0x61, 0x70, 0x65, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x66, 0x72, 0x69, 0x6e, 0x67, 0x65, 0x2f,
	0x63, 0x6f, 0x6d, 0x65, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x3b, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_comet_proto_rawDescOnce sync.Once
	file_proto_comet_proto_rawDescData = file_proto_comet_proto_rawDesc
)

func file_proto_comet_proto_rawDescGZIP() []byte {
	file_proto_comet_proto_rawDescOnce.Do(func() {
		file_proto_comet_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_comet_proto_rawDescData)
	})
	return file_proto_comet_proto_rawDescData
}

var file_proto_comet_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_comet_proto_goTypes = []any{
	(*HandleRequest)(nil),  // 0: comet.proto.HandleRequest
	(*HandleResponse)(nil), // 1: comet.proto.HandleResponse
}
var file_proto_comet_proto_depIdxs = []int32{
	0, // 0: comet.proto.CometService.Handle:input_type -> comet.proto.HandleRequest
	1, // 1: comet.proto.CometService.Handle:output_type -> comet.proto.HandleResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_comet_proto_init() }
func file_proto_comet_proto_init() {
	if File_proto_comet_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_comet_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*HandleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_comet_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*HandleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_comet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_comet_proto_goTypes,
		DependencyIndexes: file_proto_comet_proto_depIdxs,
		MessageInfos:      file_proto_comet_proto_msgTypes,
	}.Build()
	File_proto_comet_proto = out.File
	file_proto_comet_proto_rawDesc = nil
	file_proto_comet_proto_goTypes = nil
	file_proto_comet_proto_depIdxs = nil
}
