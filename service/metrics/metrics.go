package metrics

import "gitlab.papegames.com/fringe/sparrow/pkg/metric"

var (
	MetricsConnectionTotal = metric.NewGaugeVec(&metric.GaugeVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "connection_total",
		Help:        "comet connection total",
		Labels:      []string{"proto"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsBizTotal = metric.NewGaugeVec(&metric.GaugeVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "biz_connection_total",
		Help:        "comet biz total",
		Labels:      []string{"group"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsMessageSend = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "message_send",
		Help:        "comet message send total",
		Labels:      []string{"kind"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsErrorOccurred = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "error_occurred",
		Help:        "comet error occurred total",
		Labels:      []string{"kind", "reason"},
		ConstLabels: metric.TargetLabels,
	})

	MetricsBroadcastMs = metric.NewHistogramVec(&metric.HistogramVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "broadcast_milliseconds",
		Help:        "comet broadcast milliseconds",
		Labels:      []string{"kind"},
		ConstLabels: metric.TargetLabels,
		Buckets:     metric.BucketRT,
	})

	MetrcsUpstreamMessage = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "upstream_message",
		Help:        "comet upstream message total",
		Labels:      []string{"group", "state"},
		ConstLabels: metric.TargetLabels,
	})

	MetrcsRemoveEmptyScopes = metric.NewCounterVec(&metric.CounterVecOpts{
		Namespace:   "comet",
		Subsystem:   "",
		Name:        "empty_scopes_removed",
		Help:        "empty scopes removed total",
		Labels:      []string{"state"},
		ConstLabels: metric.TargetLabels,
	})
)
