package core

import (
	"testing"

	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
)

func TestUnicase(t *testing.T) {
	InitManager(10, 10, 10, 10, nil)
	manager.SetScopes(NewSession(nil, manager, nil), map[string]struct{}{
		"a": struct{}{},
		"b": struct{}{},
	})
	manager.SetScopes(NewSession(nil, manager, nil), map[string]struct{}{
		"a": struct{}{},
		"c": struct{}{},
	})

	data := []struct {
		c    string
		want bool
	}{
		{
			c:    "a",
			want: false,
		},
		{
			c:    "c",
			want: true,
		},
	}
	Convey("test unicase ", t, func() {
		for _, d := range data {
			So(manager.unicast(d.c), ShouldEqual, d.want)
		}
	})
}
