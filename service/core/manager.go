package core

import (
	"sync"
	"time"

	"gitlab.papegames.com/fringe/comet/service/metrics"
	"gitlab.papegames.com/fringe/comet/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var manager *Manager

func InitManager(bcParallel, ucParallel, brchSize, ucchSize int,
	fn func(s *Session, msg []byte) error) {
	manager = NewManager(bcParallel, ucParallel, brchSize, ucchSize, fn)
}

func GetManager() *Manager {
	return manager
}

type Manager struct {
	open           bool
	scopes         *ScopesShard
	brCh           chan *Message
	ucCh           chan *Message
	bcParallel     int
	ucParallel     int
	messageHandler func(s *Session, msg []byte) error
}

func NewManager(bcParallel, ucParallel, brchSize, ucchSize int, fn func(s *Session, msg []byte) error) *Manager {
	m := &Manager{
		open:           true,
		scopes:         NewScopesShard(32),
		brCh:           make(chan *Message, brchSize),
		ucCh:           make(chan *Message, ucchSize),
		bcParallel:     bcParallel,
		ucParallel:     ucParallel,
		messageHandler: fn,
	}
	m.run()
	return m
}

func (h *Manager) SetScopes(s *Session, m map[string]struct{}) {
	var channels []string
	for k := range m {
		shard := h.scopes.Get(k)
		scopes, _ := shard.LoadOrStore(k, new(sync.Map))
		scopes.(*sync.Map).Store(s, struct{}{})
		channels = append(channels, k)
	}
	xlog.Info("register",
		xlog.Strings("channels", channels),
		xlog.String("session", string(s.Id())),
	)
}

func (h *Manager) unicast(c string) bool {
	shard := h.scopes.Get(c)
	v, ok := shard.Load(c)
	if !ok {
		return true
	}
	n := 0
	v.(*sync.Map).Range(func(key, value any) bool {
		n++
		return n <= 1
	})
	return n <= 1
}

func (h *Manager) Unregister(s *Session) {
	var channel []string
	for k := range s.scopes {
		shard := h.scopes.Get(k)
		if v, ok := shard.Load(k); ok {
			scopes := v.(*sync.Map)
			scopes.Delete(s)
			//remove scopes is empty
			n := 0
			scopes.Range(func(key, value any) bool {
				n++
				return true
			})
			if n == 0 {
				shard.Delete(k)
				xlog.Debug("remove_empty_scopes", xlog.String("channel", k))
				metrics.MetrcsRemoveEmptyScopes.Inc("succ")
			}
			channel = append(channel, k)
		}
	}
	xlog.Info("unregister",
		xlog.Strings("channels", channel),
		xlog.String("sessions", string(s.Id())),
	)
}

func (h *Manager) Broadcast(m *Message) error {
	// only check first channel
	ch := h.brCh
	if h.unicast(m.Channel[0]) {
		ch = h.ucCh
	}
	ch <- m
	return nil
}

func (h *Manager) run() {
	for i := 0; i < h.bcParallel; i++ {
		safe.Go(func() {
			for m := range h.brCh {
				h.broadcast(m, false)
			}
		})
	}

	for i := 0; i < h.ucParallel; i++ {
		safe.Go(func() {
			for m := range h.ucCh {
				h.broadcast(m, true)
			}
		})
	}
}

func (h *Manager) broadcast(m *Message, uni bool) {
	for _, ch := range m.Channel {
		shard := h.scopes.Get(ch)
		scope, ok := shard.Load(ch)
		name := "broadcast"
		if uni {
			name = "unicast"
		}
		ctx := m.GetContext()
		logger := xlog.FromContext(ctx)
		if ok {
			metrics.MetricsMessageSend.Add(1, name)
			st := time.Now()
			logger.Debug("start sending message", xlog.String("type", name),
				xlog.String("channel", ch), xlog.ByteString("message", m.Msg))
			n := 0
			errNum := 0
			scope.(*sync.Map).Range(func(s, value any) bool {
				ss := s.(*Session)
				if err := ss.WriteMessage(m); err != nil {
					logger.Error(name, xlog.Err(err))
					metrics.MetricsErrorOccurred.Add(1, name, "write_message_failed")
					errNum++
				} else {
					logger.Debug("send_message_success", xlog.String("type", name),
						xlog.String("channel", ch))
				}
				n++
				return true
			})
			//channel no clients
			if n == 0 {
				logger.Debug("send_message_failed", xlog.String("channel", ch),
					xlog.String("reason", "channel_no_clients"))
			} else {
				if errNum > 0 && n > errNum {
					logger.Error("send_message_partial_failed", xlog.String("channel", ch),
						xlog.Int("clients_num", n), xlog.Int("err_num", errNum))
				} else if errNum > 0 && n == errNum {
					logger.Info("send_message_failed", xlog.String("type", name),
						xlog.String("channel", ch), xlog.Int("clients_num", n),
						xlog.Int("err_num", errNum))
				} else {
					logger.Info("send_message_success", xlog.String("type", name),
						xlog.String("channel", ch), xlog.Int("clients_num", n))
				}
			}
			metrics.MetricsBroadcastMs.Observe(float64(time.Since(st).Milliseconds()), name)
		} else {
			logger.Debug("send_message_failed", xlog.String("channel", ch),
				xlog.String("reason", "channel_no_clients"))
		}
	}
}

func (h *Manager) Exit(m *Message) {
	h.open = false
}

func (h *Manager) Closed() bool {
	return !h.open
}

func (h *Manager) SessionRun(conn Conner, proto string) error {
	session := NewSession(nil, h, conn)
	session.SetMessageHandler(h.messageHandler)
	metrics.MetricsConnectionTotal.Inc(proto)
	session.ReadLoop()
	if !manager.Closed() {
		manager.Unregister(session)
	}
	session.Close()
	metrics.MetricsConnectionTotal.Add(-1, proto)
	return shared.ErrClosed
}
