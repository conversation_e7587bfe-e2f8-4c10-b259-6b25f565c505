package core

import (
	"context"

	"github.com/gorilla/websocket"
)

type FilterFunc func(*Session) bool

const (
	TextMessage   = websocket.TextMessage
	BinaryMessage = websocket.BinaryMessage
	CloseMessage  = websocket.CloseMessage
	PingMessage   = websocket.PingMessage
	PongMessage   = websocket.PongMessage
)

type Message struct {
	ctx     context.Context
	T       int
	Msg     []byte
	Channel []string
}

func (m *Message) SetContext(ctx context.Context) {
	m.ctx = ctx
}

func (m *Message) GetContext() context.Context {
	return m.ctx
}
