package core

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"gitlab.papegames.com/fringe/comet/service/metrics"

	"github.com/gorilla/websocket"
	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type Session struct {
	sync.RWMutex
	scopes         map[string]string
	conn           Conner
	manager        *Manager
	open           bool
	messageHandler func(s *Session, msg []byte) error
	lastReport     time.Time
	writeLock      sync.Mutex
}

func NewSession(scopes map[string]string, m *Manager, conn Conner) *Session {
	return &Session{
		scopes:    scopes,
		conn:      conn,
		manager:   m,
		open:      true,
		writeLock: sync.Mutex{},
	}
}

func (s *Session) WriteMessage(message *Message) error {
	if s.closed() {
		return nil
	}

	deadline := time.Now().Add(config.Get().Comet.WriteTimeout)

	s.writeLock.Lock()
	defer s.writeLock.Unlock()

	s.conn.SetWriteDeadline(deadline)
	err := s.conn.WriteMessage(message)
	if err != nil {
		xlog.Error("write message", xlog.Err(err))
	}
	if message.T == websocket.CloseMessage {
		s.close()
	}
	return err
}

func (s *Session) closed() bool {
	return !s.open
}

func (s *Session) close() {
	open := s.open
	s.open = false
	if open {
		s.conn.Close()
		biz := s.Biz()
		for _, b := range biz {
			metrics.MetricsBizTotal.Add(-1, b)
		}
	}
}

func (s *Session) ReadLoop() {
	for {
		s.conn.SetReadDeadline(time.Now().Add(config.Get().Comet.HeartbeatInterval * 3))
		message, err := s.conn.ReadMessage()
		if err != nil {
			// almost all errors are caused by the client closing the connection,so use info level to log
			xlog.Debug("read message", xlog.Err(err))
			return
		}
		if config.Get().Comet.ConcurrentMessageHandling {
			safe.Go(func() {
				err := s.handleMessage(message)
				if err != nil {
					xlog.Error("handle message", xlog.Err(err))
					return
				}
			})
		} else {
			err := s.handleMessage(message)
			if err != nil {
				xlog.Error("handle message", xlog.Err(err))
				return
			}
		}
	}
}

func (s *Session) handleMessage(msg *Message) error {
	var err error
	switch msg.T {
	case websocket.TextMessage:
		// /s.HandleMessage(message)
	case websocket.BinaryMessage:
		err = s.HandleMessage(msg.Msg)
	case websocket.PingMessage:
		err = s.conn.WriteMessage(&Message{T: websocket.PongMessage, Msg: msg.Msg})
	case websocket.PongMessage: //do nothing

	case websocket.CloseMessage:
		err = s.Close()
	}
	return err
}

func (s *Session) Write(msg []byte) error {
	if s.closed() {
		return shared.ErrSessionClosed
	}
	return s.WriteMessage(&Message{T: websocket.BinaryMessage, Msg: msg})
}

func (s *Session) WriteBinary(msg []byte) error {
	if s.closed() {
		return shared.ErrSessionClosed
	}
	return s.WriteMessage(&Message{T: websocket.BinaryMessage, Msg: msg})
}

func (s *Session) Close() error {
	if s.closed() {
		return shared.ErrSessionClosed
	}
	return s.WriteMessage(&Message{T: websocket.CloseMessage,
		Msg: websocket.FormatCloseMessage(websocket.CloseNormalClosure, "")})
}

func (s *Session) CloseWithMsg(msg []byte) error {
	if s.closed() {
		return shared.ErrSessionClosed
	}
	return s.WriteMessage(&Message{T: websocket.CloseMessage,
		Msg: websocket.FormatCloseMessage(websocket.CloseUnsupportedData, string(msg))})
}

func (s *Session) IsClosed() bool {
	return s.closed()
}

func (s *Session) ScopesLen() int {
	s.RLock()
	defer s.RUnlock()
	return len(s.scopes)
}

func (s *Session) SetScopes(m map[string]struct{}) error {
	s.Lock()
	defer s.Unlock()
	if s.scopes == nil {
		s.scopes = make(map[string]string)
	}
	scopesMap := make(map[string]struct{})
	var biz []string
	for k := range m {
		topic, ch, permission, err := parseCh(k)
		if err != nil {
			return err
		}
		k := topic + POUND + ch
		// 判断是否已经存在，防止Metrics重复计数
		_, ok := s.scopes[k]
		s.scopes[k] = permission
		scopesMap[k] = struct{}{}
		if !strings.Contains(k, ":all") && !ok { // 非包含：all的和新加入的
			b, _, _ := strings.Cut(k, COLON)
			biz = append(biz, b)
		}
	}
	s.manager.SetScopes(s, scopesMap)
	// metrics
	for _, b := range biz {
		metrics.MetricsBizTotal.Inc(b)
	}
	return nil
}

func (s *Session) Id() []byte {
	return []byte(fmt.Sprintf("%#p", s))
}

func (s *Session) SetMessageHandler(fn func(*Session, []byte) error) {
	s.messageHandler = fn
}

func (s *Session) HandleMessage(msg []byte) error {
	return s.messageHandler(s, msg)
}

func (s *Session) CheckPERM(channel string) (string, error) {
	biz, ch, permission, err := parseCh(channel)
	if err != nil {
		return "", err
	}
	if biz == "" {
		return "", shared.ErrInvalidChannel
	}
	s.RLock()
	defer s.RUnlock()
	for k, v := range s.scopes {
		if k == biz+POUND+ch {
			if len(permission) == 2 || strings.Contains(v, permission) {
				return biz, nil
			}
		}
	}
	return biz, shared.ErrPermissionDenied
}

// Info for debug
func (s *Session) Info() []byte {
	s.RLock()
	defer s.RUnlock()
	info := make(map[string]any)
	info["id"] = string(s.Id())
	info["scopes"] = s.scopes
	b, _ := json.Marshal(info)
	return b
}

func (s *Session) GetScopes() []string {
	s.RLock()
	defer s.RUnlock()
	var ss []string
	for k := range s.scopes {
		ss = append(ss, k)
	}
	return ss
}

func (s *Session) LastReportTime() time.Time {
	return s.lastReport
}

func (s *Session) SetLastReportTime(t time.Time) {
	s.lastReport = t
}

func (s *Session) Biz() []string {
	s.RLock()
	defer s.RUnlock()
	var biz []string
	for k := range s.scopes {
		if !strings.Contains(k, ":all") {
			b, _, _ := strings.Cut(k, COLON)
			biz = append(biz, b)
		}
	}
	return biz
}
