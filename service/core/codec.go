package core

import (
	"bytes"

	"gitlab.papegames.com/fringe/comet/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/xencoding/json"
)

var (
	Ver   = "1"
	Delim = []byte("\r\n")
)

type CmdType string

const (
	CmdTypeSub   CmdType = "subscribe"
	CmdTypeHb    CmdType = "heartbeat"
	CmdTypeClose CmdType = "close"
	CmdTypeMsg   CmdType = "message"
)

var allowedCmds = map[CmdType]struct{}{
	CmdTypeSub:   {},
	CmdTypeHb:    {},
	CmdTypeClose: {},
	CmdTypeMsg:   {},
}

type Option struct {
	Channel string            `json:"channel,omitempty"`
	Header  map[string]string `json:"header,omitempty"`
	Module  string            `json:"module"`
}

type Header struct {
	Command   CmdType `json:"command"`
	Version   string  `json:"version"`
	RequestId string  `json:"request_id"`
	Option    Option  `json:"option,omitempty"`
}

func (h *Header) ToBytes() ([]byte, error) {
	b, err := json.Marshal(h)
	if err != nil {
		return nil, err
	}
	return b, nil
}

func Parse(b []byte) (*Header, []byte, error) {
	if b == nil {
		return nil, nil, nil
	}
	parts := bytes.SplitN(b, Delim, 2)
	var h Header
	if err := json.Unmarshal(parts[0], &h); err != nil {
		return nil, nil, err
	}
	if h.Version != Ver {
		return nil, nil, shared.ErrInvalidVersion
	}
	if _, ok := allowedCmds[CmdType(h.Command)]; !ok {
		return nil, nil, shared.ErrInvalidCommand
	}
	if len(parts) == 2 {
		return &h, parts[1], nil
	} else {
		return &h, nil, nil
	}
}

type Response struct {
	Command   CmdType  `json:"command"`
	Version   string   `json:"version"`
	RequestId string   `json:"request_id"`
	Code      int      `json:"code"`
	Info      string   `json:"info"`
	Details   []string `json:"details,omitempty"`
}

func OK(cmd CmdType, requestId string) *Response {
	return &Response{
		Command:   cmd,
		Version:   Ver,
		Code:      0,
		RequestId: requestId,
		Info:      "OK",
	}
}
func Err(cmd CmdType, i int, s string, requestId string, details ...string) *Response {
	return &Response{
		Command:   cmd,
		Version:   Ver,
		RequestId: requestId,
		Code:      i,
		Info:      s,
		Details:   details,
	}
}

func (r *Response) ToBytes() ([]byte, error) {
	b, err := json.Marshal(r)
	if err != nil {
		return nil, err
	}
	return b, nil
}

type StreamMessage struct {
	Header  *Header
	Payload []byte
}

func NewStreamMessage(h *Header, payload []byte) *StreamMessage {
	return &StreamMessage{
		Header:  h,
		Payload: payload,
	}
}

func (h *StreamMessage) ToBytes() ([]byte, error) {
	b, err := json.Marshal(h.Header)
	if err != nil {
		return nil, err
	}
	if len(h.Payload) > 0 {
		b = append(b, Delim...)
		b = append(b, h.Payload...)
	}
	return b, nil
}
