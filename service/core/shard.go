package core

import (
	"sync"

	"gitlab.papegames.com/fringe/sparrow/pkg/xhash/murmur3"
)

type ScopesShard struct {
	store []*sync.Map
}

func NewScopesShard(capacity int) *ScopesShard {
	ss := make([]*sync.Map, 32)
	for i := range ss {
		ss[i] = new(sync.Map)
	}
	return &ScopesShard{
		store: ss,
	}
}

func (s *ScopesShard) Get(channel string) *sync.Map {
	n := murmur3.Sum32([]byte(channel))
	return s.store[int(n%32)]
}
