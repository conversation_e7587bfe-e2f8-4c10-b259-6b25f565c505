package core

import (
	"strings"

	"gitlab.papegames.com/fringe/comet/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/xstring"
)

const (
	COLON = ":"
	SLASH = "/"
	POUND = "#"
)

var (
	alllowPerm = []string{"rw", "r", "w"}
)

// parseCh channel parser exp topic#channel/permission => topic, channel, permission
func parseCh(ch string) (string, string, string, error) {
	parts := strings.Split(ch, SLASH)
	if len(parts) != 2 {
		return "", "", "", shared.ErrInvalidChannel
	}
	partOne, permission := parts[0], parts[1]
	if !xstring.Contains(alllowPerm, permission) {
		return "", "", "", shared.ErrInvalidPermission
	}
	parts = strings.SplitN(partOne, POUND, 2)
	if len(parts) != 2 {
		return "", "", "", shared.ErrInvalidChannel
	}
	topic, channel := parts[0], parts[1]
	return topic, channel, permission, nil
}
