package service

import (
	"context"

	gppb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet-graph/shared/xgrpc"
	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/service/metrics"
	"gitlab.papegames.com/fringe/sparrow/pkg/client"
)

var (
	service *Service = new(Service)
)

func Get() *Service {
	return service
}

type Service struct {
	grpcClient *xgrpc.Client
}

func Startup() error {
	service.grpcClient = xgrpc.NewClient(config.Get().CometGraph.SdName)
	err := service.grpcClient.Init(
		client.GRPCDialOptions(),
	)
	if err != nil {
		return err
	}
	return nil
}

func HandleUpstreamMessage(biz string, channel string, msg []byte) error {
	// 这边需要保证一个channel分分配到同一个comet-graph上，要不然有乱序可能
	conn, err := service.grpcClient.GetConn(config.Get().CometGraph.SdName, "", channel, &xgrpc.Hash{})
	if err != nil {
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), config.Get().Comet.UpstreamTimeout)
	defer cancel()
	client := gppb.NewCometGraphServiceClient(conn)
	_, err = client.UpstreamData(ctx, &gppb.UpstreamDataRequest{
		Biz:     biz,
		Message: msg,
		Channel: channel,
	})
	if err != nil {
		metrics.MetrcsUpstreamMessage.Add(1, biz, "failed")
		return err
	} else {
		metrics.MetrcsUpstreamMessage.Add(1, biz, "success")
		return nil
	}
}

func ResponseOk(s *core.Session, c core.CmdType, requestId string) error {
	b, err := core.OK(c, requestId).ToBytes()
	if err != nil {
		return err
	}
	err = s.Write(b)
	if err != nil {
		return err
	}
	return nil
}

func ResponseErr(s *core.Session, c core.CmdType, code int, message string, requestId string, details ...string) error {
	b, err := core.Err(c, code, message, requestId, details...).ToBytes()
	if err != nil {
		return err
	}
	err = s.Write(b)
	if err != nil {
		return err
	}
	return nil
}
