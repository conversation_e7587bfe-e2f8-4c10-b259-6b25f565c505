package jwt

import (
	"fmt"
	"sync/atomic"

	"gitlab.papegames.com/fringe/comet/config"
	shared "gitlab.papegames.com/fringe/comet/shared"
	xcipher "gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var h *Jwt

type Jwt struct {
	cipher atomic.Pointer[map[string]*xcipher.Token]
}

func Get() *Jwt {
	return h
}
func Startup() error {
	h = New()
	xconf.RegisterReload(Reload)
	return Reload()
}

func New() *Jwt {
	h := new(Jwt)
	return h
}

func (h *Jwt) Parse(tokenStr string) (*Claims, error) {
	header, err := xcipher.ParseTokenHeader(tokenStr)
	if err != nil {
		xlog.Error("parse jwt token failed",
			xlog.String("token", tokenStr),
			xlog.Err(err))
		return nil, err
	}
	biz := header["kid"]
	if biz == "" {
		// default
		biz = "sdk"
	}
	xciphers := h.cipher.Load()
	xcipher, ok := (*xciphers)[biz]
	if !ok {
		return nil, shared.ErrBizNotFound
	}
	claims := new(Claims)
	err = xcipher.ParseWithClaims(tokenStr, claims)
	if err != nil {
		xlog.Error("parse jwt token failed",
			xlog.String("token", tokenStr),
			xlog.Err(err))
		return nil, err
	}
	return claims, nil
}

func Reload() error {
	biz := config.Get().Biz
	xciphers := make(map[string]*xcipher.Token)
	for k, v := range biz {
		xcipher, err := xcipher.NewToken(
			"HS256",
			v,
		)
		if err != nil {
			return fmt.Errorf("xcipher.NewToken with error(%w)", err)
		}
		xciphers[k] = xcipher
	}
	h.cipher.Store(&xciphers)
	return nil
}
