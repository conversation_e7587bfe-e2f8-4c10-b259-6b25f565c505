package jwt

import (
	"os"
	"testing"
	"time"

	"gitlab.papegames.com/fringe/comet/config"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
)

func TestJwt(t *testing.T) {
	file, err := os.CreateTemp("/tmp", "jwt-test*.yaml")
	if err != nil {
		panic(err)
	}
	file.Close()

	fname := file.Name()
	defer os.Remove(fname)

	xconf.SetConfigFile(fname)
	reload := func(b []byte) {
		So(os.WriteFile(fname, b, 0644), ShouldBeNil)
		So(xconf.Reload(), ShouldBeNil)
	}
	Convey("JWT.HS256", t, func() {
		strConfig := []byte(`
biz:
  sdk: "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6"
  test: "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy7"
`)
		reload(strConfig)
		// jwtConfig := StdConfig()
		So(config.Startup(), ShouldBeNil)
		So(Startup(), ShouldBeNil)
		tokenStr := testGenerateJWTToken(365*24*time.Hour, "HS256", config.Get().Biz["sdk"], "sdk")
		t.Log(tokenStr)
		_, err := Get().Parse(tokenStr)
		So(err, ShouldBeNil)
		tokenStr = testGenerateJWTToken(365*24*time.Hour, "HS256", config.Get().Biz["test"], "test")
		t.Log(tokenStr)
		_, err = Get().Parse(tokenStr)
		So(err, ShouldBeNil)
	})
}
func testGenerateJWTToken(exp time.Duration, alg string, secret any, kid string) string {
	cipher, err := jwt.NewToken(alg, secret, kid)
	if err != nil {
		panic(err)
	}

	claims := new(Claims)
	claims.Audience = "comet"
	claims.ExpiresAt = time.Now().Add(1 * time.Minute).Unix()
	claims.Id = "2300434"
	claims.Scopes = []string{"sdk#1033:2300434/rw", "sdk#awe:transfer:1033:all/rw"}
	tokenStr, err := cipher.Sign(claims)
	if err != nil {
		panic(err)
	}
	return tokenStr
}

func TestParseJWTToken(t *testing.T) {
	Convey("ParseJWTToken", t, func() {
		cipher, err := jwt.NewToken("HS256", "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy7", "test")
		if err != nil {
			panic(err)
		}
		claims, err := cipher.Parse("eyJhbGciOiJIUzI1NiIsImtpZCI6InRlc3QifQ.eyJhdWQiOiJjb21ldCIsImV4cCI6MTcxNjQ0Njc4MSwianRpIjoiMjMwMDQzNCIsInNjb3BlcyI6WyJzZGsjMTAzMzoyMzAwNDM0L3J3Iiwic2RrIzEwMzM6YWxsL3J3Il19.tyT-82duXcogO-UqD03bPJBlMFCBSK9SuzwZs_Pe23I")
		So(err, ShouldBeNil)
		t.Log(claims)
	})
}
