package ws

import (
	"time"

	"github.com/gorilla/websocket"
	"gitlab.papegames.com/fringe/comet/service/core"
)

type Conn struct {
	Conn *websocket.Conn
}

func (c *Conn) Close() error {
	return c.Conn.Close()
}

func (c *Conn) WriteMessage(message *core.Message) error {
	return c.Conn.WriteMessage(message.T, message.Msg)
}

func (c *Conn) SetReadDeadline(time time.Time) {
	c.Conn.SetReadDeadline(time)
}

func (c *Conn) SetWriteDeadline(time time.Time) {
	c.Conn.SetWriteDeadline(time)
}

func (c *Conn) ReadMessage() (*core.Message, error) {
	msgType, msg, err := c.Conn.ReadMessage()
	if err != nil {
		return nil, err
	}
	return &core.Message{
		T:   msgType,
		Msg: msg,
	}, nil
}
