package grpc

import (
	"context"
	"io"
	"time"

	pb "gitlab.papegames.com/fringe/comet/proto"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

type Conn struct {
	readDeadline time.Time
	ctx          context.Context
	Stream       pb.CometService_HandleServer
	closed       bool
}

func NewConn(ctx context.Context, stream pb.CometService_HandleServer) *Conn {
	return &Conn{ctx: ctx, Stream: stream}
}

func (c *Conn) Close() error {
	c.closed = true
	return nil
}

func (c *Conn) WriteMessage(message *core.Message) error {
	if c.closed {
		return io.EOF
	}
	//grpc only handle BinaryMessage
	if message.T == core.BinaryMessage {
		msg := new(pb.HandleResponse)
		msg.Message = message.Msg
		xlog.Debug("grpc write message", xlog.ByteString("payload", message.Msg))
		return c.Stream.SendMsg(msg)
	} else {
		return nil
	}
}

func (c *Conn) SetReadDeadline(time time.Time) {
	c.readDeadline = time
}

func (c *Conn) SetWriteDeadline(time time.Time) {

}

func (c *Conn) ReadMessage() (*core.Message, error) {
	if c.closed {
		return nil, io.EOF
	}
	msg := new(pb.HandleRequest)
	err := c.Stream.RecvMsg(msg)
	if err == io.EOF { //remote close
		c.closed = true
		return &core.Message{
			T: core.CloseMessage,
		}, nil
	}
	if err != nil {
		return nil, err
	}
	return &core.Message{
		T:   core.BinaryMessage,
		Msg: msg.GetMessage(),
	}, nil

}
