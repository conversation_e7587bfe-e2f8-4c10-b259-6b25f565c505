package stat

import (
	"context"
	"testing"

	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/database"
	. "gitlab.papegames.com/fringe/sparrow/pkg/testing/xconvey"
	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
)

func TestReportStatus(t *testing.T) {
	err := xconf.ReadInConfig()
	if err != nil {
		panic(err)
	}
	err = database.Startup()
	if err != nil {
		panic(err)
	}

	err = config.Startup()
	if err != nil {
		panic(err)
	}
	Convey("ReportStatus", t, func() {
		err := ReportStatus([]string{"nx.aa", "nx.ab", "nx.ac"})
		So(err, ShouldBeNil)
		ret, err := database.GetRdb().MGet(context.TODO(), "nx.aa", "nx.ab", "nx.ac").Result()
		So(err, ShouldBeNil)
		for i := 0; i < 3; i++ {
			So(ret[i], ShouldEqual, "1")
		}
		ttl1 := database.GetRdb().TTL(context.TODO(), "nx.aa").Val()
		ttl2 := database.GetRdb().TTL(context.TODO(), "nx.ab").Val()
		ttl3 := database.GetRdb().TTL(context.TODO(), "nx.ac").Val()

		expected := []any{config.Get().Comet.HeartbeatInterval * 2, config.Get().Comet.HeartbeatInterval * 3}
		So(ttl1, ShouldBeBetweenOrEqual, expected...)
		So(ttl2, ShouldBeBetweenOrEqual, expected...)
		So(ttl3, ShouldBeBetweenOrEqual, expected...)
	})
}
