package stat

import (
	"context"
	"strings"

	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/database"
)

func ReportStatus(scopes []string) error {
	var keys []string
	for _, scope := range scopes {
		//filter suffix `:all` scope
		if !strings.HasSuffix(scope, ":all") {
			keys = append(keys, scope)
		}
	}
	if len(keys) == 0 {
		return nil
	}
	ctx := context.TODO()
	p := database.GetRdb().Pipeline()
	for _, k := range keys {
		p.SetNX(ctx, k, 1, config.Get().Comet.HeartbeatInterval*3) // ace 服务会覆盖这个value
		p.Expire(ctx, k, config.Get().Comet.HeartbeatInterval*3)
	}
	_, err := p.Exec(ctx)
	return err
}
