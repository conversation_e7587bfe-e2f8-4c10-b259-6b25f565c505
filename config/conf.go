package config

import (
	"sync/atomic"
	"time"

	"gitlab.papegames.com/fringe/sparrow/pkg/xconf"
	_ "gitlab.papegames.com/fringe/sparrow/pkg/xconf/remote"
)

var conf atomic.Pointer[Config]

func Get() *Config { return conf.Load() }

type Comet struct {
	HeartbeatInterval         time.Duration `xconf:"heartbeat_interval"`
	MaxMessageSize            int64         `xconf:"max_message_size"`
	BroadcastBufferSize       int           `xconf:"broadcast_buffer_size"`
	UnicastBufferSize         int           `xconf:"unicast_buffer_size"`
	BroadcastParallel         int           `xconf:"broadcast_parallel"`
	UnicastParallel           int           `xconf:"unicast_parallel"`
	ConcurrentMessageHandling bool          `xconf:"concurrent_message_handling"`
	WriteTimeout              time.Duration `xconf:"write_timeout"`
	UpstreamTimeout           time.Duration `xconf:"upstream_timeout"`
}

type Host struct {
	WS   string `xconf:"ws"`
	Grpc string `xconf:"grpc"`
	Api  string `xconf:"api"`
}

type CometGraph struct {
	SdName string `xconf:"sd_name"`
}

type Config struct {
	Comet        Comet             `xconf:"comet"`
	Host         Host              `xconf:"host"`
	Register     bool              `xconf:"register"`
	CometGraph   CometGraph        `xconf:"comet_graph"`
	Biz          map[string]string `xconf:"biz"`
	ReportStatus bool              `xconf:"report_status"`
}

func Startup() error {
	xconf.RegisterReload(Reload)
	return Reload()
}

func Reload() error {
	c := &Config{
		Comet:    defaultComet(),
		Register: false,
	}
	err := xconf.Unmarshal(c)
	if err != nil {
		return err
	}
	conf.Store(c)
	return nil
}

func defaultComet() Comet {
	return Comet{
		HeartbeatInterval:         90 * time.Second,
		MaxMessageSize:            512 * 1024,
		ConcurrentMessageHandling: false, //default enabled
		UpstreamTimeout:           time.Second,
	}
}
