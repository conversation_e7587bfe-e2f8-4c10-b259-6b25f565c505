Bin=comet
StressBin=comet-stress
BuildTag:=$(shell git describe --abbrev=0 --tags --always)
BuildCommit=$(shell git rev-parse ${BuildTag}^0)
BuildTime=$(shell date '+%FT%T%:z')
BuildDate=$(shell date '+%Y%m%d')
BuildDateTime=$(shell date '+%Y%m%d%H%M')
TAG=release.${Bin}.${BuildDateTime}
BuildFlag="\
-X gitlab.papegames.com/fringe/sparrow/pkg.AppName=${Bin} \
-X gitlab.papegames.com/fringe/sparrow/pkg.BuildTag=${BuildTag} \
-X gitlab.papegames.com/fringe/sparrow/pkg.BuildCommit=${BuildCommit} \
-X gitlab.papegames.com/fringe/sparrow/pkg.BuildTime=${BuildTime} \
"

default: build

build: proto
	@go version
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ${Bin} -ldflags ${BuildFlag}

buildstress:
	@go version
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build  -o ${StressBin} -ldflags ${BuildFlag}  tests/stress/main.go

archive: build
	@rm -rf build && mkdir -p build
	@cp -f ${Bin} scripts/restart.sh ./build
	@cd build && tar -zcf ${Bin}-${BuildTag}-${BuildDate}.tar.gz ./*
	@cd build &&  md5sum  ${Bin}-${BuildTag}-${BuildDate}.tar.gz

build-docker:
	@go version
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
		go build -o ${Bin} -ldflags ${BuildFlag}

build-stress-docker:
	@go version
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
		go build -o ${StressBin} -ldflags ${BuildFlag} test/stress/main.go

dbuild:
	docker build -t dev-dockerhub.papegames.com/platform/comet/comet:0.0.1 . 

dsbuild:
	docker build -f test/stress/Dockerfile  -t dev-dockerhub.papegames.com/platform/comet/comet-stress:0.0.1 .

dpush:
	docker push dev-dockerhub.papegames.com/platform/comet/comet:0.0.1

dspush:
	docker push dev-dockerhub.papegames.com/platform/comet/comet-stress:0.0.1

lint:
	golangci-lint run

test:
	go test -race ./...

test-coverage:
	go test -race -timeout 30m -cover -coverprofile=./coverage.data ./...
	@go tool cover -html=./coverage.data -o ./coverage.html
	@go tool cover -func=coverage.data

proto:
	@echo "build proto"
	@protoc proto/*.proto  \
		--go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative 

tag:
	git commit -a -m "chore: release ${TAG}"; \
    git push;  \
    git tag ${TAG}; \
    git push origin ${TAG}

todev: build
	@ssh -p 28822  root@10.212.31.26 "rm -rf /data/app/comet/comet"
	@scp -P 28822 -r comet root@10.212.31.26:/data/app/comet/comet
	@ssh -p 28822  root@10.212.31.26 "cd /data/app/comet && ./restart.sh"

tostress: buildstress
	@ssh -p 28822  root@10.212.31.27 "rm -rf /data/app/comet/comet-stress"
	@scp -P 28822 -r comet-stress root@10.212.31.27:/data/app/comet/comet-stress	
	 
.PHONY: default build archive lint test test-coverage docker proto todev tostrees tag dpush dbuild build-docker dsbuild dspush
 