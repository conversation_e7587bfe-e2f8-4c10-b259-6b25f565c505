package server

import (
	"context"
	"net/http"
	"sync"

	"github.com/gorilla/websocket"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/log"
	"gitlab.papegames.com/fringe/sparrow/pkg/hooks/trace"
	"google.golang.org/grpc"

	gppb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/control"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/sparrow/pkg"
	"gitlab.papegames.com/fringe/sparrow/pkg/safe"
	spaServer "gitlab.papegames.com/fringe/sparrow/pkg/server"
	"gitlab.papegames.com/fringe/sparrow/pkg/server/xgrpc"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

const (
	ProtoWS = "ws"
)

var server = &Server{
	HttpServer: new(http.Server),
	Upgrader: &websocket.Upgrader{
		// ReadBufferSize and WriteBufferSize specify I/O buffer sizes in bytes. If a buffer
		// size is zero, then buffers allocated by the HTTP server are used. The
		// I/O buffer sizes do not limit the size of the messages that can be sent
		// or received.
		ReadBufferSize:  0,
		WriteBufferSize: 0,
		CheckOrigin:     func(r *http.Request) bool { return true },
	},
}

func Get() *Server {
	return server
}

type Server struct {
	HttpServer *http.Server
	ApiServer  *xgrpc.Server
	manager    *core.Manager
	Config     *config.Comet
	Upgrader   *websocket.Upgrader
	ctx        context.Context
	cancel     context.CancelFunc
	opts       *spaServer.Options
}

func Startup() error {
	cometC := config.Get().Comet
	core.InitManager(cometC.BroadcastParallel, cometC.UnicastBufferSize,
		cometC.BroadcastBufferSize, cometC.UnicastBufferSize, messageHandler)
	server.Config = &cometC
	ctx, cancel := context.WithCancel(context.Background())
	server.cancel = cancel
	server.ctx = ctx
	server.manager = core.GetManager()
	server.Init(
		spaServer.ServiceName(pkg.AppName),
		spaServer.ServiceHost(config.Get().Host.Api),
		spaServer.ServiceRegistrar(config.Get().Register),
	)
	return nil
}

// implement sparrow Server
func (s *Server) Name() string {
	return "comet"
}

// implement sparrow Server
func (s *Server) Init(options ...spaServer.Option) {
	s.opts = spaServer.NewOptions(options...)
}

// implement sparrow Server
func (s *Server) Serve() error {
	wg := sync.WaitGroup{}
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		if err := s.ServeWS(); err != nil {
			xlog.Error("ServeWS failed", xlog.Err(err))
		}
	})
	wg.Add(1)
	safe.Go(func() {
		defer wg.Done()
		if err := s.ServeCometApi(); err != nil {
			xlog.Error("ServeCometApi failed", xlog.Err(err))
		}
	})
	wg.Wait()
	return nil
}

// implement sparrow Server
func (s *Server) Stop() error {
	s.cancel()
	if !s.manager.Closed() {
		s.manager.Exit(&core.Message{T: core.CloseMessage})
	}
	if err := s.HttpServer.Close(); err != nil {
		return err
	}
	s.ApiServer.Stop()
	return nil
}

// implement sparrow Server
func (s *Server) GracefulStop() error {
	return s.Stop()
}

func (s *Server) ServeCometApi() error {
	s.ApiServer = xgrpc.NewServer()
	hooks.Append(trace.New())
	hooks.Append(log.New())
	s.ApiServer.Init(
		spaServer.ServiceName(pkg.AppName),
		spaServer.ServiceHost(config.Get().Host.Api),
		spaServer.ServiceRegistrar(config.Get().Register),
		spaServer.GRPCServer(&control.ApiControl{Manager: s.manager}),
		spaServer.GRPCServerRegister(gppb.RegisterCometServer),
		spaServer.GRPCServerOptions(grpc.ChainUnaryInterceptor(hooks.GetInterceptor()...)),
	)
	return s.ApiServer.Serve()
}
