package server

import (
	"net/http"

	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/control"
	wsConn "gitlab.papegames.com/fringe/comet/service/conn/ws"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/shared"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xunsafe"
)

var messageHandler = func(s *core.Session, msg []byte) error {
	h, payload, err := core.Parse(msg)
	if err != nil {
		s.CloseWithMsg([]byte(err.Error()))
		return err
	}
	switch h.Command {
	case core.CmdTypeHb:
		err = control.GetWsControl().Heartbeat(s, h)
	case core.CmdTypeSub:
		xlog.Debug("got message", xlog.Any("header", h), xlog.String("payload", xunsafe.Bytes2Str(payload)))
		err = control.GetWsControl().Subscribe(s, h, payload)
	case core.CmdTypeMsg:
		xlog.Debug("got message", xlog.Any("header", h), xlog.String("payload", xunsafe.Bytes2Str(payload)))
		err = control.GetWsControl().Message(s, h, msg)
	case core.CmdTypeClose:
		xlog.Debug("got message", xlog.Any("header", h), xlog.String("payload", xunsafe.Bytes2Str(payload)))
		err = control.GetWsControl().Close(s, h)
	}
	if err != nil {
		s.CloseWithMsg([]byte(err.Error()))
		return err
	}
	return nil
}

func (s *Server) HandleRequest(w http.ResponseWriter, r *http.Request) error {
	if s.manager.Closed() {
		return shared.ErrClosed
	}
	c, err := s.Upgrader.Upgrade(w, r, w.Header())
	if err != nil {
		return err
	}
	conn := &wsConn.Conn{Conn: c}
	return s.manager.SessionRun(conn, ProtoWS)
}

func (s *Server) ServeWS() error {
	mux := http.NewServeMux()
	//health check
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("ok"))
	})

	mux.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		s.HandleRequest(w, r)
	})
	s.HttpServer.Handler = mux
	s.HttpServer.Addr = config.Get().Host.WS
	err := s.HttpServer.ListenAndServe()
	if err != nil && err != http.ErrServerClosed {
		xlog.Error("websocket server init failed", xlog.Err(err))
		return err
	}
	return nil
}
