package control

import (
	"context"
	"strings"

	pb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
	"gitlab.papegames.com/fringe/sparrow/pkg/xtype"
)

type ApiControl struct {
	pb.UnimplementedCometServer
	Manager *core.Manager
}

func (c *ApiControl) Broadcast(ctx context.Context, request *pb.BCRequest) (*xtype.Empty, error) {
	logger := xlog.FromContext(ctx)
	h, _, err := core.Parse(request.GetMessage())
	if err != nil {
		logger.Error("parse message failed", xlog.Err(err))
		return nil, err
	}
	if h.Option.Channel != "" {
		//got channel topic#channel/permission drop permission got topic#channel
		parts := strings.Split(h.Option.Channel, core.SLASH)
		message := &core.Message{
			T:       core.BinaryMessage,
			Channel: []string{parts[0]},
			Msg:     request.GetMessage(),
		}
		message.SetContext(ctx)
		err = c.Manager.Broadcast(message)
		if err != nil {
			return nil, err
		}
	}
	return nil, nil
}
