package control

import (
	"encoding/base64"
	"strings"
	"time"

	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/service"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	"gitlab.papegames.com/fringe/comet/service/metrics"
	"gitlab.papegames.com/fringe/comet/service/stat"
	"gitlab.papegames.com/fringe/sparrow/pkg/xencoding/json"
	"gitlab.papegames.com/fringe/sparrow/pkg/xlog"
)

var wsControl = new(WsControl)

type WsControl struct{}

func GetWsControl() *WsControl {
	return wsControl
}

func (WsControl) Subscribe(s *core.Session, h *core.Header, payload []byte) error {
	var scopes []string
	err := json.Unmarshal(payload, &scopes)
	if err != nil || len(scopes) == 0 {
		xlog.Error("invalid paylod",
			xlog.String("payload", string(payload)),
			xlog.Err(err))
		service.ResponseErr(s, h.Command, 10004, "invalid payload", h.RequestId)
		if s.ScopesLen() > 0 {
			return nil
		} else {
			return err
		}
	}
	scopeMap := make(map[string]struct{})
	var details []string
	for _, scope := range scopes {
		if j, err := jwt.Get().Parse(scope); err != nil {
			tmep := strings.Split(scope, ".")
			var jwtPayload []byte
			if len(tmep) > 1 {
				jwtPayload, _ = base64.RawURLEncoding.DecodeString(tmep[1])
			}
			xlog.Error("parse jwt token failed",
				xlog.String("token", scope), xlog.String("jwt payload", string(jwtPayload)),
				xlog.Err(err))
			details = append(details, scope)
		} else {
			for _, k := range j.Scopes {
				scopeMap[k] = struct{}{}
			}
		}
	}
	// 批量订阅，返回有问题的token，这个时候不断开连接且全部订阅失败
	if len(details) > 0 {
		service.ResponseErr(s, h.Command, 10001, "parse jwt token failed", h.RequestId, details...)
		return nil
	}
	err = s.SetScopes(scopeMap)
	if err != nil {
		xlog.Error("SetScope failed",
			xlog.Any("scopes", scopeMap),
			xlog.Err(err))
		service.ResponseErr(s, h.Command, 10002, err.Error(), h.RequestId)
		return err
	}
	reportSessionStat(s)
	err = service.ResponseOk(s, h.Command, h.RequestId)
	if err != nil {
		return err
	}
	return nil
}

func (WsControl) Message(s *core.Session, h *core.Header, rawMessage []byte) error {
	if h.Option.Channel != "" {
		biz, err := s.CheckPERM(h.Option.Channel)
		if err != nil {
			xlog.Info("CheckPERM with error",
				xlog.String("channel", h.Option.Channel),
				xlog.Strings("scopes", s.GetScopes()))
			service.ResponseErr(s, h.Command, 10003, err.Error(), h.RequestId)
			return nil // don't close connection
		}
		err = service.HandleUpstreamMessage(biz, h.Option.Channel, rawMessage)
		if err != nil {
			xlog.Error("broadcast failed", xlog.String("biz", biz), xlog.Err(err))
			service.ResponseErr(s, h.Command, 10003, err.Error(), h.RequestId)
			metrics.MetricsErrorOccurred.Add(1, "upstream", "handle_upstream_message")
			return nil
		}
		reportSessionStat(s)
		err = service.ResponseOk(s, h.Command, h.RequestId)
		if err != nil {
			xlog.Error("responseOk failed", xlog.String("cmdtype", string(h.Command)), xlog.Err(err))
			return nil
		}
	} else {
		err := service.ResponseErr(s, h.Command, 10003, "channel is empty", h.RequestId)
		if err != nil {
			xlog.Error("service.ResponseErr failed", xlog.String("cmdtype", string(h.Command)), xlog.Err(err))
			return nil // don't close connection
		}
	}
	return nil
}

func (WsControl) Close(s *core.Session, h *core.Header) error {
	err := service.ResponseOk(s, h.Command, h.RequestId)
	if err != nil {
		return err
	}
	s.Close()
	return nil
}

func (WsControl) Heartbeat(s *core.Session, h *core.Header) error {
	err := service.ResponseOk(s, h.Command, h.RequestId)
	if err != nil {
		return err
	}
	reportSessionStat(s)
	return nil
}

func reportSessionStat(s *core.Session) {
	if config.Get().ReportStatus {
		if time.Since(s.LastReportTime()) < 5*time.Second {
			return
		}
		if err := stat.ReportStatus(s.GetScopes()); err != nil {
			xlog.Error("ReportStatus failed", xlog.Err(err))
			metrics.MetricsErrorOccurred.Add(1, "report_status", "report_status_failed")
			return
		}
		s.SetLastReportTime(time.Now())
	}
}
