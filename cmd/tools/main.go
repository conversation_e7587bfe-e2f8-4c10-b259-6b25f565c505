package main

import (
	"context"
	"encoding/json"
	"flag"
	"log"
	"net/http"

	pb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet/service/core"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	url := flag.String("url", "comet-graph-test.papegames.com:8080", "url")
	flag.Parse()

	http.HandleFunc("/web", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./web/index.html")
	})
	http.HandleFunc("/web/main.js", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, "./web/main.js")
	})

	http.HandleFunc("/send", func(w http.ResponseWriter, r *http.Request) {
		r.<PERSON>rseForm()
		header := r.Form.Get("header")
		message := r.Form.Get("message")
		log.Printf("header: %s, message: %s", header, message)
		h := new(core.Header)
		err := json.Unmarshal([]byte(header), h)
		if err != nil {
			w.Write([]byte("json decode failed" + err.Error()))
			return
		}
		conn, err := grpc.Dial(*url,
			grpc.WithTransportCredentials(insecure.NewCredentials()))
		if err != nil {
			log.Fatalf("fail to dial: %v", err)
		}
		defer conn.Close()
		client := pb.NewCometGraphServiceClient(conn)
		msg := core.StreamMessage{
			Header:  h,
			Payload: []byte(message),
		}
		b, err := msg.ToBytes()
		if err != nil {
			w.Write([]byte(err.Error()))
			return
		}
		_, err = client.Broadcast(context.TODO(), &pb.BroadcastRequest{Message: b})
		if err != nil {
			w.Write([]byte(err.Error()))
			return
		}
		w.Write([]byte("ok"))
	})
	http.ListenAndServe(":9080", nil)
}
