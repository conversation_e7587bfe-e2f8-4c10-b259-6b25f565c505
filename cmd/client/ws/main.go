package main

import (
	"flag"
	"log"
	"net/url"
	"os"
	"time"

	"github.com/gorilla/websocket"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	xcipher "gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
)

// comet-test.papegames.com test
// comet-pre.papegames.com pre国内
// comet-pre.infoldgames.com  pre 海外
// comet.papegames.com prod 国内
// comet.infoldgames.com  prod 海外

var addr = flag.String("addr", "comet.infoldgames.com", "http service address")
var isTLS = flag.Bool("tls", false, "use websocket over tls")

func main() {
	flag.Parse()
	log.SetFlags(0)

	Scheme := "ws"
	if *isTLS {
		Scheme = "wss"
	}
	u := url.URL{Scheme: Scheme, Host: *addr, Path: "/ws"}
	log.Printf("connecting to %s", u.String())

	c, _, err := websocket.DefaultDialer.Dial(u.String(), nil)
	if err != nil {
		log.Fatal("dial:", err)
	}
	defer c.Close()

	done := make(chan struct{})

	jwtToken, err := xcipher.NewToken("HS256", "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6")
	if err != nil {
		panic(err)
	}
	now := time.Now()
	ttl := 24 * time.Hour
	cliams := new(jwt.Claims)
	cliams.Audience = "sparrow"
	cliams.ExpiresAt = now.Add(ttl).Unix()
	cliams.Scopes = []string{"sdk#1136:6103732/rw", "sdk#1136:all/rw", "acepc#1136:6103732/rw"}
	cliams.Subject = "subject"
	token, err := jwtToken.Sign(cliams)
	if err != nil {
		panic(err)
	}
	go func() {
		defer close(done)
		for {
			_, message, err := c.ReadMessage()
			if websocket.IsCloseError(err) {
				os.Exit(0)
			}
			if err != nil {
				log.Println("read:", err)
				return
			}

			h, m, _ := core.Parse(message)
			if h.Command == core.CmdTypeMsg {
				if len(m) > 0 {
					log.Printf("recv: %s", m)
				}
			}
			log.Printf("recv: %s", message)
		}
	}()

	subMsg := core.StreamMessage{
		Header: &core.Header{
			Command: core.CmdTypeSub,
			Version: core.Ver,
		},
		Payload: []byte("[\"" + token + "\"]"),
	}

	b, err := subMsg.ToBytes()
	if err != nil {
		log.Println("send:", err)
		return
	}
	c.WriteMessage(websocket.BinaryMessage, b)

	pubMsg := core.Header{
		Command: core.CmdTypeMsg,
		Option: core.Option{
			Channel: "acepc#1136:6103732/rw",
		},
		Version: core.Ver,
	}
	b, err = pubMsg.ToBytes()
	if err != nil {
		panic(err)
	}

	for {
		time.Sleep(3 * time.Second)
		payload := append(b, []byte("\r\n")...)
		payload = append(payload, []byte("say hello from ws client")...)
		c.WriteMessage(websocket.BinaryMessage, payload)
	}

	// for i := 0; i < 10; i++ {
	// 	time.Sleep(3 * time.Second)
	// }
}
