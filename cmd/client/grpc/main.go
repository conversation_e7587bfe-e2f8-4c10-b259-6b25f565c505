package main

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"time"

	pb "gitlab.papegames.com/fringe/comet/proto"
	"gitlab.papegames.com/fringe/comet/service/core"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	xcipher "gitlab.papegames.com/fringe/sparrow/pkg/xcipher/jwt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	//************
	conn, err := grpc.Dial("**************:5000",
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("fail to dial: %v", err)
	}
	defer conn.Close()
	client := pb.NewCometServiceClient(conn)
	CometSub(client)
}

func CometSub(client pb.CometServiceClient) {
	// ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	// defer cancel()
	stream, err := client.Handle(context.Background())
	if err != nil {
		log.Fatalf("client.RouteChat failed: %v", err)
	}
	waitc := make(chan struct{})

	jwtToken, err := xcipher.NewToken("HS256", "86vvT6c9XytdT6VPStAmJswuJw2zSgupGDRfumy6")
	if err != nil {
		panic(err)
	}
	now := time.Now()
	ttl := 60 * time.Second

	cliams := new(jwt.Claims)
	cliams.Audience = "sparrow"
	cliams.ExpiresAt = now.Add(ttl).Unix()
	cliams.Scopes = []string{"sdk#1106:2300434/rw"}
	cliams.Subject = "subject"

	token, err := jwtToken.Sign(cliams)
	if err != nil {
		panic(err)
	}

	subMsg := core.StreamMessage{
		Header: &core.Header{
			Command: core.CmdTypeSub,
			Option: core.Option{
				Channel: "sdk#1106:2300434/rw",
			},
			Version: core.Ver,
		},
		Payload: []byte("[\"" + token + "\"]"),
	}

	b, err := subMsg.ToBytes()
	if err != nil {
		log.Println("send:", err)
		return
	}
	stream.Send(&pb.HandleRequest{
		Message: b,
	})

	go func() {
		for {
			in, err := stream.Recv()
			if err == io.EOF {
				close(waitc)
				return
			}
			if err != nil {
				log.Fatalf("client.Subscribe failed: %v", err)
			}
			part := bytes.Split(in.Message, []byte("\r\n"))
			if len(part) < 2 {
				continue
			}
			fmt.Println(string(part[1]))
		}
	}()

	pubMsg := core.Header{
		Command: core.CmdTypeMsg,
		Option: core.Option{
			Channel: "sdk#1001:1/rw",
		},
		Version: core.Ver,
	}
	b, err = pubMsg.ToBytes()
	if err != nil {
		panic(err)
	}

	for i := 0; i < 10000000; i++ {
		time.Sleep(3 * time.Second)
		payload := append(b, []byte("\r\n")...)
		payload = append(payload, []byte(fmt.Sprintf("say hello from grpc client %07d", i))...)
		if err := stream.Send(&pb.HandleRequest{
			Message: payload,
		}); err != nil {
			log.Fatalf("client.Subscribe: stream.Send(%v) failed: %v", payload, err)
		}
	}
	stream.CloseSend()
	<-waitc
}
