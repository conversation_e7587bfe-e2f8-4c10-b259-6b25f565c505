package main

import (
	"context"
	"log"

	pb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet/service/core"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

func main() {
	conn, err := grpc.Dial("172.23.209.225:5002",
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("fail to dial: %v", err)
	}
	defer conn.Close()
	client := pb.NewCometClient(conn)
	msg := core.StreamMessage{
		Header: &core.Header{
			Command: core.CmdTypeMsg,
			Version: "1",
			Option: core.Option{
				Channel: "sdk#1001:1/rw",
			},
		},
		Payload: []byte("hello"),
	}
	b, err := msg.ToBytes()
	if err != nil {
		panic(err)
	}
	for i := 0; i < 100; i++ {
		_, err = client.Broadcast(context.TODO(), &pb.BCRequest{Message: b})
		if err != nil {
			panic(err)
		}
	}
}
