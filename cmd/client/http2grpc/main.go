package main

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	pb "gitlab.papegames.com/fringe/comet-graph/proto"
	"gitlab.papegames.com/fringe/comet/service/core"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var client pb.CometClient

func main() {
	conn, err := grpc.Dial("10.149.53.2:8000",
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("fail to dial: %v", err)
	}
	defer conn.Close()
	client = pb.NewCometClient(conn)

	http.HandleFunc("/test", func(w http.ResponseWriter, r *http.Request) {
		phone := r.URL.Query().Get("phone")
		channel := r.URL.Query().Get("channel")
		nonce := r.URL.Query().Get("nonce")
		code, _ := strconv.ParseInt(r.URL.Query().Get("code"), 10, 64)
		log.Printf("phone: %s, channel: %s ,nonce: %s", phone, channel, nonce)
		call(phone, channel, nonce, code)
	})

	http.ListenAndServe(":8160", nil)

}

func call(phone, channel, nonce string, code int64) {
	data := map[string]any{
		"data": map[string]any{
			"risk": map[string]any{
				"nonce": nonce,
				"phone": []string{
					phone[0:3],
					phone[7:],
				},
			},
		},
		"code": code,
	}

	dd, _ := json.Marshal(data)
	msg := core.StreamMessage{
		Header: &core.Header{
			Command: core.CmdTypeMsg,
			Version: "1",
			Option: core.Option{
				Channel: channel,
				Module:  "risk",
			},
		},
		Payload: dd,
	}
	b, err := msg.ToBytes()
	if err != nil {
		panic(err)
	}
	_, err = client.Broadcast(context.TODO(), &pb.BCRequest{Message: b})
	if err != nil {
		panic(err)
	}
}
