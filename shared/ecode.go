package shared

import (
	"gitlab.papegames.com/fringe/sparrow/pkg/ecode"
)

var (
	ErrClosed              = ecode.New(10000, "server is closed")
	ErrSessionClosed       = ecode.New(10001, "session is closed")
	ErrWriteClosed         = ecode.New(10002, "tried to write to closed a session")
	ErrBroadcastBufferFull = ecode.New(10003, "broadcast buffer is full")
	ErrInvalidMessage      = ecode.New(10004, "invalid message")
	ErrInvalidCommand      = ecode.New(10005, "invalid command")
	ErrInvalidVersion      = ecode.New(10006, "invalid version")
	ErrInvalidChannel      = ecode.New(10007, "invalid channel")
	ErrInvalidPermission   = ecode.New(10008, "invalid permission")
	ErrPermissionDenied    = ecode.New(10009, "permission denied")
	ErrBizNotFound         = ecode.New(10011, "biz not found")
)
