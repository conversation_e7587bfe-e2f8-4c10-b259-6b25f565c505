package main

import (
	"gitlab.papegames.com/fringe/comet/config"
	"gitlab.papegames.com/fringe/comet/database"
	"gitlab.papegames.com/fringe/comet/server"
	"gitlab.papegames.com/fringe/comet/service"
	"gitlab.papegames.com/fringe/comet/service/jwt"
	"gitlab.papegames.com/fringe/sparrow"
)

func main() {
	app := new(sparrow.Application)
	app.Startup(
		config.Startup,
		jwt.Startup,
		service.Startup,
		server.Startup,
		database.Startup,
	).Server(server.Get()).Launch()
}
